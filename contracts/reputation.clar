;; TaskBanc Reputation Contract
;; Manages user reputation, staking, and slashing mechanisms

(use-trait sip010-trait 'SP3FBR2AGK5H9QBDH3EEN6DF8EK8JY7RX8QJ5SVTE.sip-010-trait-ft-standard.sip-010-trait)

;; Data maps
(define-map reputation principal uint)
(define-map stakes { staker: principal, bounty-id: uint }
  {
    amount: uint,
    token: (optional principal),
    block-height: uint,
    active: bool
  })

;; Data vars
(define-data-var total-reputation uint u0)

;; Events
(define-private (emit-reputation-change (user principal) (old-rep uint) (new-rep uint))
  (print {
    event: "reputation-change",
    user: user,
    old-reputation: old-rep,
    new-reputation: new-rep,
    block-height: block-height
  }))

(define-private (emit-stake-event (staker principal) (bounty-id uint) (amount uint) (action (string-ascii 20)))
  (print {
    event: "stake-action",
    staker: staker,
    bounty-id: bounty-id,
    amount: amount,
    action: action,
    block-height: block-height
  }))

;; Get user reputation
(define-read-only (get-reputation (user principal))
  (default-to u0 (map-get? reputation user)))

;; Get stake information
(define-read-only (get-stake (staker principal) (bounty-id uint))
  (map-get? stakes { staker: staker, bounty-id: bounty-id }))

;; Check if user has minimum reputation
(define-read-only (has-min-reputation (user principal))
  (>= (get-reputation user) (var-get MIN_REPUTATION)))

;; Increase reputation (only callable by authorized contracts)
(define-public (increase-reputation (user principal) (amount uint))
  (begin
    ;; Only contract calls allowed
    (asserts! (not (is-eq contract-caller tx-sender)) (err u1000))
    (asserts! (> amount u0) (err u1001))

    (let ((current-rep (get-reputation user))
          (new-rep (+ current-rep amount)))
      ;; Update reputation
      (map-set reputation user new-rep)
      ;; Update total
      (var-set total-reputation (+ (var-get total-reputation) amount))
      ;; Emit event
      (emit-reputation-change user current-rep new-rep)
      (ok new-rep))))

;; Decrease reputation with floor at 0 (only callable by authorized contracts)
(define-public (decrease-reputation (user principal) (amount uint))
  (begin
    ;; Only contract calls allowed
    (asserts! (not (is-eq contract-caller tx-sender)) (err u1000))
    (asserts! (> amount u0) (err u1001))

    (let ((current-rep (get-reputation user))
          (new-rep (if (>= current-rep amount) (- current-rep amount) u0))
          (actual-decrease (- current-rep new-rep)))
      ;; Update reputation
      (map-set reputation user new-rep)
      ;; Update total
      (var-set total-reputation (- (var-get total-reputation) actual-decrease))
      ;; Emit event
      (emit-reputation-change user current-rep new-rep)
      (ok new-rep))))

;; Stake tokens for a bounty
(define-public (stake-for-bounty
    (bounty-id uint)
    (amount uint)
    (token (optional principal)))
  (begin
    (asserts! (> amount u0) (err u1001))
    (asserts! (>= amount (var-get MIN_STAKE_AMOUNT)) (err u1010))

    ;; Check if stake already exists
    (asserts! (is-none (get-stake tx-sender bounty-id)) (err u1004))

    ;; Verify user has sufficient funds
    (asserts!
      (if (is-some token)
        ;; For SIP-010 tokens, we'd need to check balance
        ;; For now, assume transfer will validate
        true
        ;; For STX
        (>= (stx-get-balance tx-sender) amount))
      (err u1005))

    ;; Create stake record
    (map-set stakes
      { staker: tx-sender, bounty-id: bounty-id }
      {
        amount: amount,
        token: token,
        block-height: block-height,
        active: true
      })

    ;; Emit event
    (emit-stake-event tx-sender bounty-id amount "stake")
    (ok true)))

;; Slash stake (only callable by arbitration contract)
(define-public (slash-stake (staker principal) (bounty-id uint) (slash-amount uint))
  (begin
    ;; Only contract calls allowed
    (asserts! (not (is-eq contract-caller tx-sender)) (err u1000))

    (match (get-stake staker bounty-id)
      stake-info
        (begin
          (asserts! (get active stake-info) (err u1002))
          (let ((current-amount (get amount stake-info))
                (remaining (if (>= current-amount slash-amount)
                             (- current-amount slash-amount)
                             u0)))
            ;; Update stake
            (map-set stakes
              { staker: staker, bounty-id: bounty-id }
              (merge stake-info { amount: remaining, active: (> remaining u0) }))

            ;; Decrease reputation proportionally
            (let ((rep-decrease (/ (* slash-amount u100) current-amount)))
              (try! (decrease-reputation staker rep-decrease)))

            ;; Emit event
            (emit-stake-event staker bounty-id slash-amount "slash")
            (ok remaining)))
      (err u1003))))

;; Release stake (only callable by escrow/arbitration contracts)
(define-public (release-stake (staker principal) (bounty-id uint))
  (begin
    ;; Only contract calls allowed
    (asserts! (not (is-eq contract-caller tx-sender)) (err u1000))

    (match (get-stake staker bounty-id)
      stake-info
        (begin
          (asserts! (get active stake-info) (err u1002))
          ;; Mark stake as inactive
          (map-set stakes
            { staker: staker, bounty-id: bounty-id }
            (merge stake-info { active: false }))

          ;; Increase reputation for successful completion
          (try! (increase-reputation staker u50))

          ;; Emit event
          (emit-stake-event staker bounty-id (get amount stake-info) "release")
          (ok (get amount stake-info)))
      (err u1003))))

;; Check if user has active stake for bounty
(define-read-only (has-active-stake (staker principal) (bounty-id uint))
  (match (get-stake staker bounty-id)
    stake-info (get active stake-info)
    false))

;; Get total active stakes for user
(define-read-only (get-total-active-stakes (user principal))
  ;; This is a simplified implementation
  ;; In practice, you'd iterate through stakes
  u0)

;; Administrative function to initialize reputation for testing
(define-public (init-reputation (user principal) (amount uint))
  (begin
    ;; Only allow during testing/setup
    (asserts! (is-eq tx-sender (var-get contract-owner)) (err u1000))
    (map-set reputation user amount)
    (ok true)))

;; Contract owner (for admin functions)
(define-data-var contract-owner principal tx-sender)

;; Constants (imported from utils)
(define-data-var MIN_REPUTATION uint u100)
(define-data-var MIN_STAKE_AMOUNT uint u1000000)