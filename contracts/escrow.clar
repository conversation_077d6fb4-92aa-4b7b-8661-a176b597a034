;; TaskBanc Escrow Contract
;; Handles custody and release of funds for bounties

(use-trait sip010-trait 'SP3FBR2AGK5H9QBDH3EEN6DF8EK8JY7RX8QJ5SVTE.sip-010-trait-ft-standard.sip-010-trait)

;; Data maps
(define-map escrows uint
  {
    bounty-id: uint,
    depositor: principal,
    amount: uint,
    token: (optional principal),
    released: bool,
    recipient: (optional principal),
    block-height: uint
  })

;; Data vars
(define-data-var escrow-nonce uint u0)

;; Events
(define-private (emit-escrow-event (bounty-id uint) (action (string-ascii 20)) (amount uint) (participant principal))
  (print {
    event: "escrow-action",
    bounty-id: bounty-id,
    action: action,
    amount: amount,
    participant: participant,
    block-height: block-height
  }))

;; Deposit funds into escrow
(define-public (deposit (bounty-id uint) (amount uint) (token (optional principal)))
  (begin
    (asserts! (> amount u0) (err u1001))
    (asserts! (> bounty-id u0) (err u1007))

    ;; Check if escrow already exists for this bounty
    (asserts! (is-none (map-get? escrows bounty-id)) (err u1004))

    ;; Transfer funds to this contract
    (let ((transfer-result
            (if (is-some token)
              ;; SIP-010 token transfer - simplified for now
              ;; In practice, would use token-adapter contract
              (match token
                token-contract (stx-transfer? amount tx-sender (as-contract tx-sender))
                (stx-transfer? amount tx-sender (as-contract tx-sender)))
              ;; STX transfer
              (stx-transfer? amount tx-sender (as-contract tx-sender)))))

      ;; Check transfer success
      (asserts! (is-ok transfer-result) (err u1006))

      ;; Create escrow record
      (map-set escrows bounty-id
        {
          bounty-id: bounty-id,
          depositor: tx-sender,
          amount: amount,
          token: token,
          released: false,
          recipient: none,
          block-height: block-height
        })

      ;; Emit event
      (emit-escrow-event bounty-id "deposit" amount tx-sender)
      (ok bounty-id))))

;; Release funds to recipient (only callable by authorized contracts)
(define-public (release (bounty-id uint) (recipient principal))
  (begin
    ;; Only contract calls allowed
    (asserts! (not (is-eq contract-caller tx-sender)) (err u1000))

    (match (map-get? escrows bounty-id)
      escrow-info
        (begin
          (asserts! (not (get released escrow-info)) (err u1002))

          ;; Transfer funds to recipient
          (let ((amount (get amount escrow-info))
                (token (get token escrow-info))
                (transfer-result
                  (if (is-some token)
                    ;; SIP-010 token transfer
                    (as-contract (stx-transfer? amount tx-sender recipient))
                    ;; STX transfer
                    (as-contract (stx-transfer? amount tx-sender recipient)))))

            ;; Check transfer success
            (asserts! (is-ok transfer-result) (err u1006))

            ;; Mark as released
            (map-set escrows bounty-id
              (merge escrow-info { released: true, recipient: (some recipient) }))

            ;; Emit event
            (emit-escrow-event bounty-id "release" amount recipient)
            (ok true)))
      (err u1003))))

;; Refund to original depositor (only callable by authorized contracts)
(define-public (refund (bounty-id uint))
  (begin
    ;; Only contract calls allowed
    (asserts! (not (is-eq contract-caller tx-sender)) (err u1000))

    (match (map-get? escrows bounty-id)
      escrow-info
        (begin
          (asserts! (not (get released escrow-info)) (err u1002))

          ;; Transfer funds back to depositor
          (let ((amount (get amount escrow-info))
                (depositor (get depositor escrow-info))
                (token (get token escrow-info))
                (transfer-result
                  (if (is-some token)
                    ;; SIP-010 token transfer
                    (as-contract (stx-transfer? amount tx-sender depositor))
                    ;; STX transfer
                    (as-contract (stx-transfer? amount tx-sender depositor)))))

            ;; Check transfer success
            (asserts! (is-ok transfer-result) (err u1006))

            ;; Mark as released (refunded)
            (map-set escrows bounty-id
              (merge escrow-info { released: true, recipient: (some depositor) }))

            ;; Emit event
            (emit-escrow-event bounty-id "refund" amount depositor)
            (ok true)))
      (err u1003))))

;; Get escrow balance and details
(define-read-only (get-escrow-balance (bounty-id uint))
  (map-get? escrows bounty-id))

;; Check if escrow exists and is active
(define-read-only (is-escrow-active (bounty-id uint))
  (match (map-get? escrows bounty-id)
    escrow-info (not (get released escrow-info))
    false))

;; Get escrow amount
(define-read-only (get-escrow-amount (bounty-id uint))
  (match (map-get? escrows bounty-id)
    escrow-info (some (get amount escrow-info))
    none))

;; Get escrow depositor
(define-read-only (get-escrow-depositor (bounty-id uint))
  (match (map-get? escrows bounty-id)
    escrow-info (some (get depositor escrow-info))
    none))

;; Get escrow token
(define-read-only (get-escrow-token (bounty-id uint))
  (match (map-get? escrows bounty-id)
    escrow-info (get token escrow-info)
    none))

;; Check if funds are sufficient for bounty
(define-read-only (has-sufficient-escrow (bounty-id uint) (required-amount uint))
  (match (get-escrow-amount bounty-id)
    amount (>= amount required-amount)
    false))

;; Administrative function to handle emergency withdrawals
(define-public (emergency-withdraw (bounty-id uint))
  (begin
    ;; Only contract owner can perform emergency withdrawals
    (asserts! (is-eq tx-sender (var-get contract-owner)) (err u1000))

    (match (map-get? escrows bounty-id)
      escrow-info
        (begin
          ;; Only allow if not already released
          (asserts! (not (get released escrow-info)) (err u1002))

          ;; Refund to depositor
          (try! (refund bounty-id))
          (ok true))
      (err u1003))))

;; Contract owner (for emergency functions)
(define-data-var contract-owner principal tx-sender)