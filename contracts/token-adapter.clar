;; TaskBanc Token Adapter Contract
;; Unified interface for STX and SIP-010 fungible token transfers

(use-trait sip010-trait 'SP3FBR2AGK5H9QBDH3EEN6DF8EK8JY7RX8QJ5SVTE.sip-010-trait-ft-standard.sip-010-trait)
(impl-trait 'SP3FBR2AGK5H9QBDH3EEN6DF8EK8JY7RX8QJ5SVTE.sip-010-trait-ft-standard.sip-010-trait)

;; Import error codes from utils
(use-trait utils-trait .utils)

;; Transfer funds - handles both STX and SIP-010 tokens
;; token: none for STX, some for SIP-010 token contract
;; amount: amount to transfer in base units
;; sender: sender principal
;; recipient: recipient principal
(define-public (transfer-funds
    (token (optional <sip010-trait>))
    (amount uint)
    (sender principal)
    (recipient principal))
  (begin
    ;; Validate parameters
    (asserts! (> amount u0) (err u1001))
    (asserts! (not (is-eq sender recipient)) (err u1007))

    ;; Transfer based on token type
    (match token
      ;; SIP-010 token transfer
      token-contract
        (match (contract-call? token-contract transfer amount sender recipient none)
          success (ok true)
          error (err u1006))
      ;; STX transfer
      (match (stx-transfer? amount sender recipient)
        success (ok true)
        error (err u1006)))))

;; Get balance - handles both STX and SIP-010 tokens
;; token: none for STX, some for SIP-010 token contract
;; owner: principal to check balance for
(define-read-only (balance-of
    (token (optional <sip010-trait>))
    (owner principal))
  (match token
    ;; SIP-010 token balance
    token-contract
      (match (contract-call? token-contract get-balance owner)
        balance (ok balance)
        error (err u1003))
    ;; STX balance
    (ok (stx-get-balance owner))))

;; Check if transfer would succeed without executing
(define-read-only (can-transfer
    (token (optional <sip010-trait>))
    (amount uint)
    (sender principal))
  (match token
    ;; SIP-010 token check
    token-contract
      (match (contract-call? token-contract get-balance sender)
        balance (ok (>= balance amount))
        error (ok false))
    ;; STX check
    (ok (>= (stx-get-balance sender) amount))))

;; Validate token contract (basic check)
(define-read-only (is-valid-token (token <sip010-trait>))
  (is-some (contract-call? token aget-name)))

;; Get token name (for SIP-010 tokens)
(define-read-only (get-token-name (token <sip010-trait>))
  (contract-call? token get-name))

;; Get token symbol (for SIP-010 tokens)
(define-read-only (get-token-symbol (token <sip010-trait>))
  (contract-call? token get-symbol))

;; Required SIP-010 implementations (minimal for trait compliance)
(define-public (transfer (amount uint) (sender principal) (recipient principal) (memo (optional (buff 34))))
  (err u1000)) ;; Not implemented - use transfer-funds instead

(define-read-only (get-name)
  (ok "TaskBanc Token Adapter"))

(define-read-only (get-symbol)
  (ok "TBTA"))

(define-read-only (get-decimals)
  (ok u6))

(define-read-only (get-balance (who principal))
  (ok u0)) ;; Not applicable for adapter

(define-read-only (get-total-supply)
  (ok u0)) ;; Not applicable for adapter

(define-read-only (get-token-uri)
  (ok none))