;; TaskBanc Utilities Contract
;; Common constants, error codes, and enums for the TaskBanc ecosystem

;; Error codes
(define-constant ERR_UNAUTHORIZED (err u1000))
(define-constant ERR_INVALID_AMOUNT (err u1001))
(define-constant ERR_INVALID_STATUS (err u1002))
(define-constant ERR_NOT_FOUND (err u1003))
(define-constant ERR_ALREADY_EXISTS (err u1004))
(define-constant ERR_INSUFFICIENT_FUNDS (err u1005))
(define-constant ERR_TRANSFER_FAILED (err u1006))
(define-constant ERR_INVALID_PARAMETERS (err u1007))
(define-constant ERR_TIME_EXPIRED (err u1008))
(define-constant ERR_INSUFFICIENT_REPUTATION (err u1009))
(define-constant ERR_INSUFFICIENT_STAKE (err u1010))

;; Status constants
(define-constant STATUS_OPEN u1)
(define-constant STATUS_ASSIGNED u2)
(define-constant STATUS_COMPLETED u3)
(define-constant STATUS_DISPUTE u4)
(define-constant STATUS_CLOSED u5)

;; Dispute status constants
(define-constant DISPUTE_OPEN u1)
(define-constant DISPUTE_VOTING u2)
(define-constant DISPUTE_RESOLVED u3)

;; Proposal status constants
(define-constant PROPOSAL_ACTIVE u1)
(define-constant PROPOSAL_PASSED u2)
(define-constant PROPOSAL_FAILED u3)

;; Time constants (blocks)
(define-constant VOTING_PERIOD u1008) ;; ~1 week in blocks
(define-constant PROPOSAL_PERIOD u2016) ;; ~2 weeks in blocks

;; Minimum values
(define-constant MIN_REPUTATION u100)
(define-constant MIN_STAKE_AMOUNT u1000000) ;; 1 STX in microSTX

;; Helper function to check if caller is contract
(define-read-only (is-contract-caller)
  (is-eq contract-caller tx-sender))

;; Helper function to validate amounts
(define-read-only (is-valid-amount (amount uint))
  (> amount u0))

;; Helper function to validate status transition
(define-read-only (is-valid-status-transition (from uint) (to uint))
  (or
    (and (is-eq from STATUS_OPEN) (is-eq to STATUS_ASSIGNED))
    (and (is-eq from STATUS_ASSIGNED) (is-eq to STATUS_COMPLETED))
    (and (is-eq from STATUS_ASSIGNED) (is-eq to STATUS_DISPUTE))
    (and (is-eq from STATUS_DISPUTE) (is-eq to STATUS_CLOSED))
    (and (is-eq from STATUS_COMPLETED) (is-eq to STATUS_CLOSED))))

;; Helper function to check minimum reputation requirement
(define-read-only (meets-min-reputation (rep uint))
  (>= rep MIN_REPUTATION))

;; Helper function to check minimum stake requirement
(define-read-only (meets-min-stake (stake uint))
  (>= stake MIN_STAKE_AMOUNT))