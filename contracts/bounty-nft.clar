;; TaskBanc Bounty NFT Contract
;; SIP-010 compliant badges for reputation milestones

(impl-trait 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.nft-trait.nft-trait)

;; Data maps
(define-map tokens uint principal)
(define-map badge-levels uint { level: uint, name: (string-ascii 50), min-reputation: uint })
(define-map user-badges principal (list 10 uint))

;; Data vars
(define-data-var last-token-id uint u0)
(define-data-var contract-uri (optional (string-utf8 256)) none)

;; Badge level definitions
(map-set badge-levels u1 { level: u1, name: "Bronze Contributor", min-reputation: u100 })
(map-set badge-levels u2 { level: u2, name: "Silver Contributor", min-reputation: u500 })
(map-set badge-levels u3 { level: u3, name: "Gold Contributor", min-reputation: u1000 })
(map-set badge-levels u4 { level: u4, name: "Platinum Contributor", min-reputation: u2500 })
(map-set badge-levels u5 { level: u5, name: "Diamond Contributor", min-reputation: u5000 })

;; Events
(define-private (emit-badge-mint (recipient principal) (token-id uint) (level uint))
  (print {
    event: "badge-mint",
    recipient: recipient,
    token-id: token-id,
    level: level,
    block-height: block-height
  }))

;; Mint badge for user based on reputation (only callable by authorized contracts)
(define-public (mint-badge (recipient principal) (level uint))
  (begin
    ;; Only contract calls allowed (from reputation system)
    (asserts! (not (is-eq contract-caller tx-sender)) (err u1000))
    (asserts! (> level u0) (err u1007))
    (asserts! (<= level u5) (err u1007))

    ;; Check if badge level exists
    (asserts! (is-some (map-get? badge-levels level)) (err u1003))

    ;; Get next token ID
    (let ((token-id (+ (var-get last-token-id) u1)))
      ;; Mint NFT
      (map-set tokens token-id recipient)
      (var-set last-token-id token-id)

      ;; Add to user's badge list
      (let ((current-badges (default-to (list) (map-get? user-badges recipient))))
        (map-set user-badges recipient (unwrap-panic (as-max-len? (append current-badges token-id) u10))))

      ;; Emit event
      (emit-badge-mint recipient token-id level)
      (ok token-id))))

;; Manual mint by admin (for testing or special cases)
(define-public (admin-mint-badge (recipient principal) (level uint))
  (begin
    (asserts! (is-eq tx-sender (var-get contract-owner)) (err u1000))
    (try! (mint-badge recipient level))
    (ok true)))

;; Auto-mint badge based on reputation check
(define-public (check-and-mint-badge (user principal))
  (begin
    ;; Get user reputation from reputation contract
    (let ((user-rep (unwrap-panic (contract-call? .reputation get-reputation user))))
      ;; Check which badge level they qualify for
      (if (>= user-rep u5000)
        (try! (mint-badge user u5))
        (if (>= user-rep u2500)
          (try! (mint-badge user u4))
          (if (>= user-rep u1000)
            (try! (mint-badge user u3))
            (if (>= user-rep u500)
              (try! (mint-badge user u2))
              (if (>= user-rep u100)
                (try! (mint-badge user u1))
                (ok u0))))))
      (ok true