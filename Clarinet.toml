[project]
name = 'taskbanc-clarity'
description = ''
authors = []
telemetry = true
cache_dir = './.cache'
requirements = []
[contracts.bounty-nft]
path = 'contracts/bounty-nft.clar'
clarity_version = 3
epoch = 3.1

[contracts.escrow]
path = 'contracts/escrow.clar'
clarity_version = 3
epoch = 3.1

[contracts.reputation]
path = 'contracts/reputation.clar'
clarity_version = 3
epoch = 3.1

[contracts.token-adapter]
path = 'contracts/token-adapter.clar'
clarity_version = 3
epoch = 3.1

[contracts.utils]
path = 'contracts/utils.clar'
clarity_version = 3
epoch = 3.1
[repl.analysis]
passes = ['check_checker']

[repl.analysis.check_checker]
strict = false
trusted_sender = false
trusted_caller = false
callee_filter = false
